# Magentic-UI 使用指南

## 项目简介

Magentic-UI 是微软开发的一个基于多智能体系统的人机协作界面，可以：
- 🧑‍🤝‍🧑 **协作规划**：通过聊天和计划编辑器协作创建和批准分步计划
- 🤝 **协作任务**：通过网页浏览器或聊天中断和指导任务执行
- 🛡️ **操作保护**：敏感操作只有在用户明确批准后才会执行
- 🧠 **计划学习和检索**：从以前的运行中学习以改进未来的任务自动化
- 🔀 **并行任务执行**：可以并行运行多个任务

## 运行前准备

### 1. 设置OpenAI API密钥

在运行之前，你需要设置OpenAI API密钥：

```bash
export OPENAI_API_KEY="你的OpenAI_API密钥"
```

### 2. 确保Docker正在运行

```bash
sudo systemctl start docker
sudo systemctl enable docker
```

## 启动应用

### 方法1：使用启动脚本（推荐）

```bash
cd /home/<USER>/mgq/magentic-ui
export OPENAI_API_KEY="你的OpenAI_API密钥"
./start_magentic.sh
```

### 方法2：手动启动

```bash
cd /home/<USER>/mgq/magentic-ui
source .venv/bin/activate
export OPENAI_API_KEY="你的OpenAI_API密钥"
export SQLITE_THREADSAFE=1

python -c "
import sys
import pysqlite3
sys.modules['sqlite3'] = pysqlite3
from magentic_ui.backend.cli import run
run()
" ui --port 8081
```

## 访问应用

启动成功后，在浏览器中访问：
```
http://localhost:8081
```

## 界面说明

- **左侧面板**：会话导航器
  - 创建新会话
  - 切换会话
  - 查看会话状态（🔴 需要输入，✅ 任务完成，↺ 进行中）

- **右侧面板**：分为两部分
  - **左侧**：显示计划、任务进度和操作批准请求
  - **右侧**：浏览器视图，实时查看网页代理操作

## 基本使用流程

1. **输入任务**：在聊天框中描述你想要完成的任务
2. **审核计划**：系统会生成一个分步计划供你审核和编辑
3. **批准执行**：确认计划后，系统开始执行任务
4. **实时监控**：可以随时中断、指导或提供反馈

## 注意事项

1. **首次运行**会比较慢，因为需要构建Docker镜像
2. **确保网络连接稳定**，需要访问OpenAI API
3. **敏感操作**会要求用户确认
4. **可以并行运行多个任务**

## 故障排除

### 如果遇到SQLite版本问题
项目已经配置了pysqlite3来解决SQLite版本兼容性问题。

### 如果遇到Docker问题
确保Docker服务正在运行：
```bash
sudo systemctl status docker
```

### 如果遇到端口占用
可以更改端口：
```bash
python -c "..." ui --port 8082
```

## 配置选项

### 使用Azure OpenAI
如果你想使用Azure OpenAI，可以在UI中的设置页面配置，或创建config.yaml文件。

### 使用本地模型（Ollama）
如果你想使用本地模型，需要先安装Ollama支持：
```bash
pip install magentic-ui[ollama]
```

## 获取帮助

- 官方文档：https://github.com/microsoft/magentic-ui
- 问题反馈：https://github.com/microsoft/magentic-ui/issues

祝你使用愉快！🎉
