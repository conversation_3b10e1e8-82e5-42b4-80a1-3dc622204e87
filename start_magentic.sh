#!/bin/bash

# 进入项目目录
cd /home/<USER>/mgq/magentic-ui

# 激活虚拟环境
source .venv/bin/activate

# 设置SQLite环境变量
export SQLITE_THREADSAFE=1

# 检查是否设置了OpenAI API密钥
if [ -z "$OPENAI_API_KEY" ]; then
    echo "注意: 未设置 OPENAI_API_KEY 环境变量"
    echo "你可以稍后在UI界面中配置API密钥"
    echo "继续启动..."
fi

# 启动magentic-ui
echo "正在启动 Magentic-UI..."
echo "首次运行可能需要一些时间来构建Docker镜像..."

# 使用Python直接运行，并设置sqlite3模块替换
python -c "
import sys
import pysqlite3
sys.modules['sqlite3'] = pysqlite3

from magentic_ui.backend.cli import run
run()
" ui --port 8081
