.dark {
  --color-bg-primary: #1a1a1a;
  --color-bg-secondary: #a88da841;
  --color-bg-light: #3a3a3a;
  --color-bg-tertiary: #444444;
  --color-bg-opposite: #edf2f7;
  --color-bg-white: #ffffff;
  --color-bg-dark: #1a1a1a;
  --color-bg-accent: #777777;
  --color-text-primary: #ffffff;
  --color-text-secondary: #e5e5e58f;
  --color-text-accent: #e5e5e5;
  --color-border-primary: #ffffff30;
  --color-border-secondary: #e5e5e530;
  --color-border-accent: #1a1a1a;
  --color-warning-primary: #F6A147;
  --color-text-message: #000000;

  --color-magenta-400: #F2E5F2;
  --color-magenta-700: #EECBEE;
  --color-magenta-800: #BF3AC3;
  --color-magenta-900: #953CA3;

  --color-blue-400: #EDF4FB;
  --color-blue-700: #A0D6FF;
  --color-blue-800: #0078d4;
  --color-blue-900: #0062ad;

  --color-gary-700: #e1e1e1;
  --color-gray-800: #5f5f5f;
}

.light {
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f1e9f1;
  --color-bg-light: #f9fafb;
  --color-bg-tertiary: #f3f6f9;
  --color-bg-opposite: #2a2a2a;
  --color-bg-white: #ffffff;
  --color-bg-dark: #1a1a1a;
  --color-bg-accent: #444444;
  --color-text-primary: #000000;
  --color-text-secondary: #4a5568;
  --color-text-accent: #444444;
  --color-border-primary: #e2e8f0;
  --color-border-secondary: #edf2f7;
  --color-border-accent: #444444;
  --color-warning-primary: #F6A147;
  --color-text-message: #000000;

  --color-magenta-400: #F2E5F2;
  --color-magenta-700: #EECBEE;
  --color-magenta-800: #BF3AC3;
  --color-magenta-900: #953CA3;

  --color-blue-400: #EDF4FB;
  --color-blue-700: #A0D6FF;
  --color-blue-800: #0078d4;
  --color-blue-900: #0062ad;

  --color-gary-700: #e1e1e1;
  --color-gray-800: #5f5f5f;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* @layer base {
    body {
      @apply dark:bg-slate-800 dark:text-white;
    }
  } */

/* Import Open Sans font */
@font-face {
  font-family: "Open Sans";
  src: url("./Open_Sans/OpenSans-VariableFont_wdth\,wght.ttf")
    format("truetype-variations");
  font-weight: 300 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Open Sans";
  src: url("./Open_Sans/OpenSans-Italic-VariableFont_wdth\,wght.ttf")
    format("truetype-variations");
  font-weight: 300 800;
  font-style: italic;
  font-display: swap;
}

html {
  /* display: table; */
  margin: auto;
  height: 100%;
  font-family: "Segoe UI", "Open Sans", sans-serif;;
}

body {
  padding: 0px 64px 0px 64px;
  @apply px-4 md:px-8 lg:px-16 w-full !important;
  margin: 0px auto;
  background: transparent;
  /* border: 2px solid green; */
  width: 100%;
  height: 100%;
  font-size: 0.9rem; /* Ensure consistent font size */
  font-family: "Segoe UI", "Open Sans", sans-serif;;
}

* {
  font-size: 0.9rem;
}

/* @import "antd/dist/antd.css"; */

.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  @apply text-accent !important;
  font-weight: 500;
}
.ant-tabs-nav::before {
  @apply border-secondary !important;
}
.ant-tabs-tab:hover {
  @apply text-accent !important;
}

.ant-tabs-tab {
  @apply text-primary !important;
}

.ant-tabs-ink-bar {
  @apply bg-accent !important;
}

.element.style {
  left: 0%;
  width: 100%;
}
.ant-slider-track {
  @apply bg-accent !important;
}
.ant-slider-rail {
  @apply bg-secondary !important;
}
.ant-slider-handle {
  @apply border-accent !important;
}
.ant-slider-handle:hover {
  @apply border-accent brightness-75 !important;
}

.ant-switch-checked {
  @apply bg-accent !important;
}
.ant-switch {
  background-color: #3a3a3a;
  border: grey;
}

/* .ant-modal-content {
  @apply dark:bg-primary dark:text-primary;
}
.ant-modal-footer {
  @apply border-secondary;
}
.ant-modal-header,
.ant-modal-close {
  @apply bg-secondary text-primary hover:text-primary    transition duration-200;
}
.ant-modal-title,
.ant-modal-header {
  @apply bg-primary text-primary;
} */
a:hover {
  @apply text-accent;
}
/* .iiz__img,
  iiz__zoom-img {
    @apply w-full;
  } */

.ant-radio-checked .ant-radio-inner {
  @apply border-accent !important;
}

.ant-radio-checked .ant-radio-inner:after {
  @apply bg-accent !important;
}

.ant-radio:hover .ant-radio-inner {
  @apply border-accent !important;
}

.loadbar:after {
  content: "";
  /* width: 40px; */
  height: 3px;
  /* background: red; */

  position: absolute;
  animation: loader 2s;
  -webkit-animation: loader 2s;
  animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  transition-timing-function: linear;
  -webkit-transition-timing-function: linear;
  bottom: 0px;
  @apply rounded-b bg-accent;

  margin-left: 0;
}

@keyframes loader {
  0% {
    width: 0%;
    left: 0;
    right: 0;
  }
  50% {
    width: 100%;
    left: 0;
    right: 0;
  }
  99% {
    width: 0%;
    left: 100%;
    right: 0;
  }
}

@-webkit-keyframes loader {
  0% {
    width: 0%;
    left: 0;
  }
  50% {
    width: 100%;
    left: 0;
    right: 0;
  }
  99% {
    width: 0%;
    left: 100%;
    right: 0;
  }
}
.scroll::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  opacity: 0; /* Make scrollbar fully transparent by default */
  transition: opacity 0.25s ease; /* Transition for the opacity */
}

.scroll:hover::-webkit-scrollbar {
  opacity: 1; /* Make scrollbar fully opaque on hover */
}

.scroll::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 20px;
}

.scroll::-webkit-scrollbar-thumb {
  @apply bg-accent;
  border-radius: 20px;
  border: 3px solid rgb(214, 214, 214);
}

.dark .scroll-gradient {
}

.light .scroll-gradient {
}

.scroll-gradient {
  position: sticky;
  top: 0;
  height: 15px;
  z-index: 2; /* Above the content but below the scrollbar */
  pointer-events: none; /* So it doesn't block interaction with the content */
}

.vega-embed {
  @apply bg-primary rounded !important;
}

.ant-upload-list,
.ant-upload-hint,
.ant-upload-list-text,
.ant-upload-text,
.ant-upload-text-icon {
  @apply text-primary !important;
}

.ant-upload {
  @apply text-primary px-2  !important;
  border-radius: 4px !important;
}
.ant-upload:hover {
  @apply border-accent !important;
}
.ant-upload-drag-container,
.ant-upload-text,
.ant-btn,
.ant-select,
.ant-select > span,
.ant-select-selector,
.ant-form-item-required,
.ant-upload-hint {
  @apply text-primary !important;
}

.ant-upload-list-item:hover,
.ant-upload-list-item-info:hover {
  @apply bg-secondary text-accent !important;
}
.ant-pagination .ant-pagination-item,
.ant-select-selector,
.ant-btn-variant-outlined {
  @apply bg-primary  !important;
}
.ant-pagination .ant-pagination-item-active a {
  @apply text-accent  !important;
}
.ant-pagination .ant-pagination-item-active {
  @apply border-accent text-accent !important;
}
.ant-pagination .ant-pagination-item a,
.ant-pagination-item-link .anticon {
  @apply text-primary !important;
}
.ant-collapse-expand-icon .anticon {
  @apply text-primary !important;
}
.ant-modal-content {
  @apply dark:bg-primary dark:text-primary !important;
}
.ant-modal-footer {
  @apply border-secondary !important;
}
.ant-btn,
.ant-select-arrow,
.ant-btn:hover {
  @apply text-primary !important;
}

:where(.ant-btn).ant-btn-compact-item.ant-btn-primary:not([disabled])
  + .ant-btn-compact-item.ant-btn-primary:not([disabled]):before {
  @apply bg-secondary !important;
}
.ant-btn-primary {
  @apply bg-accent text-white !important;
}
.ant-btn-primary:hover {
  @apply bg-accent text-white drop-shadow-md !important;
}
.ant-modal-close {
  @apply text-primary   duration-200   !important;
}

.ant-radio,
.ant-collapse,
.ant-collapse-header-text,
.ant-collapse-content-box,
.ant-collapse-content,
.ant-radio-wrapper {
  @apply text-primary !important;
}
.ant-collapse-borderless > .ant-collapse-item {
  @apply border-secondary !important;
}

.ant-skeleton-paragraph > li {
  @apply bg-secondary !important;
}

.ant-drawer-content,
.ant-drawer-header,
.ant-drawer-header-title,
.ant-drawer-close,
.ant-drawer-title {
  @apply bg-primary text-primary !important;
}

.ant-dropdown-menu {
  max-height: 250px;
  overflow: auto;
  @apply scroll !important;
}

.ant-collapse {
  @apply bg-secondary border-primary !important;
}

.ant-collapse-header {
  @apply bg-secondary text-primary border-primary !important;
}

.ant-collapse-content {
  @apply bg-primary border-primary !important;
}

.ant-collapse-item {
  @apply border-primary !important;
}

/* .ant-radio-input::before {
    @apply bg-primary !important;
  } */

.prose > pre {
  padding: 0px !important;
  margin: 0px !important;
}

.prose p {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.monaco-editor,
.monaco-scrollable-element,
.overflow-guard {
  @apply rounded !important;
}

/* div.chatbox > ul {
    list-style: disc !important;
  }
  div.chatbox > ul,
  div.chatbox > ol {
    padding-left: 20px !important;
    margin: 0px !important;
  }
  div.chatbox > ol {
    list-style: decimal !important;
  } */

div#___gatsby,
div#gatsby-focus-wrapper {
  height: 100%;
  /* border: 1px solid green; */
}
